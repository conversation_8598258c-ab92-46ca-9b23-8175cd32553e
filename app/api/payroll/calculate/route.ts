import { NextRequest, NextResponse } from 'next/server'
import { withAuth, withPermission, ApiContext } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { payrollCalculationEngine } from '@/lib/payroll-service'
import { z } from 'zod'

const calculatePayrollSchema = z.object({
  employeeIds: z.array(z.string()).min(1, 'At least one employee ID is required'),
  period: z.string().regex(/^\d{4}-\d{2}$/, 'Period must be in YYYY-MM format'),
  startDate: z.string().datetime(),
  endDate: z.string().datetime(),
})

const calculateSingleEmployeeSchema = z.object({
  employeeId: z.string(),
  period: z.string().regex(/^\d{4}-\d{2}$/, 'Period must be in YYYY-MM format'),
  startDate: z.string().datetime(),
  endDate: z.string().datetime(),
})

async function calculatePayroll(context: ApiContext, request: NextRequest) {
  // Check if user has permission to calculate payroll
  if (!['ADMIN', 'HR', 'FINANCE'].includes(context.user.role)) {
    return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
  }

  const body = await request.json()
  const { searchParams } = new URL(request.url)
  const single = searchParams.get('single') === 'true'

  if (single) {
    // Calculate for single employee
    const validatedData = calculateSingleEmployeeSchema.parse(body)

    const result = await payrollCalculationEngine.calculateEmployeePayroll({
      employeeId: validatedData.employeeId,
      period: validatedData.period,
      startDate: new Date(validatedData.startDate),
      endDate: new Date(validatedData.endDate),
      workingDays: payrollCalculationEngine.getWorkingDaysInMonth(validatedData.period),
    })

    return NextResponse.json(result)
  } else {
      // Calculate for multiple employees
      const validatedData = calculatePayrollSchema.parse(body)
      
      const results = await payrollCalculationEngine.calculateBulkPayroll(
        validatedData.employeeIds,
        validatedData.period,
        new Date(validatedData.startDate),
        new Date(validatedData.endDate)
      )

    return NextResponse.json({
      period: validatedData.period,
      totalEmployees: validatedData.employeeIds.length,
      successfulCalculations: results.length,
      failedCalculations: validatedData.employeeIds.length - results.length,
      results,
    })
  }
}

export const POST = withAuth(calculatePayroll)